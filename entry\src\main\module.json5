{
  "module": {
    "name": "entry",
    "type": "entry",
    "description": "$string:module_desc",
    "mainElement": "EntryAbility",
    "deviceTypes": [
      "phone"
    ],
    "deliveryWithInstall": true,
    "installationFree": false,
    "pages": "$profile:main_pages",
    "abilities": [
      {
        "name": "EntryAbility",
        "srcEntry": "./ets/entryability/EntryAbility.ets",
        "description": "$string:EntryAbility_desc",
        "icon": "$media:layered_image",
        "label": "$string:EntryAbility_label",
        "startWindowIcon": "$media:startIcon",
        "startWindowBackground": "$color:start_window_background",
        "exported": true,
        "skills": [
          {
            "entities": [
              "entity.system.home"
            ],
            "actions": [
              "action.system.home",
              "ohos.nfc.cardemulation.action.HOST_APDU_SERVICE"
            ]
          }
        ],
        "metadata": [
          {
            "name": "payment-aid",
            "value": "A000000333010101"  // 中国工商银行
          },
          {
            "name": "payment-aid",
            "value": "A000000632010105"  // 北京一卡通
          },
          {
            "name": "payment-aid",
            "value": "A00000000386980701"  // 上海复旦微
          },
          {
            "name": "payment-aid",
            "value": "A000000632010105535A4B5700000533"  // 淄博卡
          },
          {
            "name": "payment-aid",
            "value": "4351515041592E5359533331"  // 重庆通卡
          },
          {
            "name": "payment-aid",
            "value": "D1560001018003800000000100000011"  // 测试钱包卡1
          },
          {
            "name": "payment-aid",
            "value": "D1560001018003800000000100000022"  // 测试钱包卡2
          },
          {
            "name": "payment-aid",
            "value": "D1560001018003800000000100000033"  // 测试钱包卡3
          },
          {
            "name": "payment-aid",
            "value": "D1560001018003800000000100000044"  // 测试钱包卡4
          },
          {
            "name": "payment-aid",
            "value": "474D534D324D6F647532"  // 门禁卡
          },
          {
            "name": "payment-aid",
            "value": "315041592E5359532E44444630310591"  // 福州卡
          },
          {
            "name": "payment-aid",
            "value": "A0000006320101050113581058000000"  // 深圳通
          },
          {
            "name": "payment-aid",
            "value": "A000000632010105535A4B5700000731"  // 长沙edep
          },
          {
            "name": "payment-aid",
            "value": "A0000006320101053000300100083010"  // 南京通卡
          },
          {
            "name": "payment-aid",
            "value": "A0000003330101020063485750415905"  // 上海Mot
          },
          {
            "name": "payment-aid",
            "value": "325041592E5359532E4444463031"  // ctsverify
          }
        ]
      },
      {
        "name": "PaymentAbility",
        "srcEntry": "./ets/paymentability/PaymentAbility.ets",
        "description": "$string:PaymentAbility_desc",
        "icon": "$media:layered_image",
        "label": "$string:EntryAbility_label",
        "startWindowIcon": "$media:startIcon",
        "startWindowBackground": "$color:start_window_background",
        "exported": true,
        "excludeFromMissions": true,
        "launchType": "singleton",
        "skills": [
          {
            "entities": [
              "entity.system.default"
            ],
            "actions": [
              "action.system.payment",
              "ohos.want.action.viewData"
            ]
          }
        ]
      }
    ],
    "extensionAbilities": [
      {
        "name": "EntryBackupAbility",
        "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets",
        "type": "backup",
        "exported": false,
        "metadata": [
          {
            "name": "ohos.extension.backup",
            "resource": "$profile:backup_config"
          }
        ],
      }
    ],
    "requestPermissions": [
      {
        "name": "ohos.permission.NFC_CARD_EMULATION",
        "reason": "$string:card_emulation_reason",
      },
      {
        "name": "ohos.permission.START_ABILITIES_FROM_BACKGROUND",
        "reason": "$string:start_abilities_from_background_reason",
      }
    ]
  }
}