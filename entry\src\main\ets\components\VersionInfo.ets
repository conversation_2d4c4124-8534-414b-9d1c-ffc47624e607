/**
 * 版本信息组件
 */
@Component
export struct VersionInfo {
  @Prop versionName?: string = '1.0.0';
  @Prop showBuildNumber?: boolean = false;
  @Prop buildNumber?: string = '';

  build() {
    Column({ space: 4 }) {
      Text(`版本 ${this.versionName}`)
        .fontSize(14)
        .fontColor('#666666')
        .textAlign(TextAlign.Center)

      if (this.showBuildNumber && this.buildNumber) {
        Text(`构建号 ${this.buildNumber}`)
          .fontSize(12)
          .fontColor('#999999')
          .textAlign(TextAlign.Center)
      }
    }
    .width('100%')
    .padding({ bottom: 16 })
    .justifyContent(FlexAlign.Center)
  }
}