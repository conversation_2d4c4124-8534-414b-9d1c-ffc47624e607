# TmsWallet - HarmonyOS NFC钱包应用

## 项目简介

TmsWallet是一款基于HarmonyOS开发的NFC钱包应用，支持HCE（Host Card Emulation）功能，可以模拟各种智能卡进行NFC支付和门禁等操作。应用支持多种卡片类型，包括银行卡、交通卡、门禁卡等。

## 核心功能

### 🏦 多卡片支持
- **银行卡**: 中国工商银行等主流银行卡
- **交通卡**: 北京一卡通、深圳通、上海交通卡等
- **门禁卡**: 支持各种门禁系统
- **测试卡**: 内置多种测试卡片用于开发调试

### 📱 HCE功能
- **卡片模拟**: 通过HCE技术模拟物理卡片
- **后台刷卡**: 支持应用在后台时进行NFC交互
- **默认支付**: 可设置为系统默认支付应用
- **APDU处理**: 完整的APDU命令处理机制

### 🔧 卡片管理
- **添加卡片**: 支持复制卡和添加新卡
- **卡片选择**: 可在多张卡片间切换
- **脚本管理**: 基于TypeScript的卡片脚本系统
- **状态管理**: 实时显示HCE服务状态

## 技术架构

### 开发环境
- **开发工具**: DevEco Studio
- **系统版本**: HarmonyOS API 12+
- **编程语言**: ArkTS (TypeScript)
- **UI框架**: ArkUI

### 核心技术栈
- **NFC通信**: @kit.ConnectivityKit
- **卡片模拟**: cardEmulation.HceService
- **文件管理**: @kit.CoreFileKit
- **权限管理**: ohos.permission.NFC_CARD_EMULATION
- **日志系统**: @kit.PerformanceAnalysisKit

### 项目结构
```
TmsWallet/
├── AppScope/                    # 应用全局配置
│   ├── app.json5               # 应用配置文件
│   └── resources/              # 全局资源文件
├── entry/                      # 主模块
│   ├── src/main/
│   │   ├── ets/
│   │   │   ├── components/     # 通用组件
│   │   │   │   ├── CardItem.ets        # 卡片项组件
│   │   │   │   ├── CommonDialog.ets    # 通用对话框
│   │   │   │   └── VersionInfo.ets     # 版本信息组件
│   │   │   ├── models/         # 数据模型和管理器
│   │   │   │   ├── CardManager.ets     # 卡片管理器
│   │   │   │   ├── HceManager.ets      # HCE功能管理器
│   │   │   │   ├── NavigationManager.ets # 导航管理器
│   │   │   │   └── ScriptManager.ets   # 脚本管理器
│   │   │   ├── pages/          # 页面组件
│   │   │   │   ├── MainPage.ets        # 主页面
│   │   │   │   ├── AddCardSubPage.ets  # 添加卡片页面
│   │   │   │   └── HceManagerSubPage.ets # HCE管理页面
│   │   │   ├── entryability/   # 应用入口
│   │   │   │   └── EntryAbility.ets    # 主Ability
│   │   │   └── entrybackupability/     # 备份功能
│   │   │       └── EntryBackupAbility.ets
│   │   ├── resources/          # 资源文件
│   │   │   ├── rawfile/script/ # 卡片脚本文件
│   │   │   ├── base/           # 基础资源
│   │   │   └── ...
│   │   └── module.json5        # 模块配置文件
│   └── oh-package.json5        # 模块依赖配置
├── oh-package.json5            # 项目依赖配置
└── build-profile.json5         # 构建配置
```

## 开发环境搭建

### 前置要求
1. **DevEco Studio**: 版本 4.0 或更高
2. **HarmonyOS SDK**: API Level 12 或更高
3. **测试设备**: 支持NFC功能的HarmonyOS设备
4. **开发者账号**: 华为开发者联盟账号

### 环境配置
1. **下载安装DevEco Studio**
   ```bash
   # 从华为开发者官网下载最新版本
   https://developer.harmonyos.com/cn/develop/deveco-studio
   ```

2. **配置SDK**
   - 打开DevEco Studio
   - 进入 Settings > HarmonyOS SDK
   - 下载对应API Level的SDK

3. **设备配置**
   - 启用开发者模式
   - 开启USB调试
   - 确保设备支持NFC功能

## 快速开始

### 1. 导入项目
1. 打开DevEco Studio
2. 选择 "Open" 导入项目
3. 等待项目同步完成

### 2. 配置签名
1. 在DevEco Studio中配置应用签名
2. 确保签名证书支持NFC权限

### 3. 运行应用
1. 连接支持NFC的HarmonyOS设备
2. 点击运行按钮或使用快捷键 Ctrl+R
3. 应用将自动安装并启动

## 核心模块详解

### HceManager - HCE功能管理
```typescript
// 初始化HCE服务
const hceManager = HceManager.getInstance();
hceManager.initialize();

// 启动后台刷卡服务
hceManager.startBackgroundService();

// 设置为默认支付应用
await hceManager.requestDefaultPaymentApp();
```

**主要功能**:
- HCE能力检测
- 服务初始化和管理
- APDU命令处理
- 默认支付应用设置

### CardManager - 卡片管理
```typescript
// 添加新卡片
const card = new CardModel(cardId, cardName, cardType);
await cardManager.addCard(card);

// 选择激活卡片
cardManager.selectCard(cardId);

// 删除卡片
cardManager.removeCard(cardId);
```

**主要功能**:
- 卡片增删改查
- 卡片状态管理
- 卡片选择切换

### ScriptManager - 脚本管理
```typescript
// 读取卡片脚本
const scriptContent = await scriptManager.readScriptFile(cardId, scriptType);

// 加载卡片ID列表
await scriptManager.loadCardIdList();
```

**主要功能**:
- 脚本文件管理
- 动态脚本加载
- 沙箱文件操作

## 支持的卡片类型

### 银行卡
- **中国工商银行**: A000000333010101
- **其他银行**: 支持标准银联AID

### 交通卡
- **北京一卡通**: A000000632010105
- **深圳通**: A0000006320101050113581058000000
- **上海交通卡**: A0000003330101020063485750415905
- **南京通卡**: A0000006320101053000300100083010
- **长沙卡**: A000000632010105535A4B5700000731
- **淄博卡**: A000000632010105535A4B5700000533
- **重庆通卡**: 4351515041592E5359533331
- **福州卡**: 315041592E5359532E44444630310591

### 门禁卡
- **通用门禁**: 474D534D324D6F647532

### 测试卡
- **测试卡1-4**: D156000101800380000000010000001X (X=1,2,3,4)

## 权限配置

### 必需权限
```json5
{
  "requestPermissions": [
    {
      "name": "ohos.permission.NFC_CARD_EMULATION",
      "reason": "$string:card_emulation_reason"
    }
  ]
}
```

### 系统能力
- `SystemCapability.Communication.NFC.Core`
- `SystemCapability.Communication.NFC.CardEmulation`

## 开发指南

### 添加新卡片类型
1. **定义AID**: 在HceManager中添加新的AID
```typescript
private readonly supportedAidList: string[] = [
  // 现有AID...
  "YOUR_NEW_AID_HERE"  // 新卡片AID
];
```
2. **创建脚本文件**: 在`resources/rawfile/script/`目录下创建对应脚本
3. **更新module.json5**: 添加新的metadata配置
```json5
{
  "name": "payment-aid",
  "value": "YOUR_NEW_AID_HERE"
}
```

### 签名配置
1. 在DevEco Studio中配置自动签名
2. 或手动配置签名文件路径

### 发布准备
1. **版本号管理**: 更新`app.json5`中的版本信息
2. **权限审核**: 确保NFC权限申请理由充分
3. **兼容性测试**: 在不同设备上测试应用功能

### 代码规范
- 遵循ArkTS编码规范
- 使用有意义的变量和函数命名
- 添加必要的注释和文档