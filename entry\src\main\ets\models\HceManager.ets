import bundleManager from '@ohos.bundle.bundleManager';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { DialogHelper } from '../components/CommonDialog';
import { cardEmulation } from '@kit.ConnectivityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { AsyncCallback } from '@kit.BasicServicesKit';
import nfcTmsController from '@ohos.nfc.tmsController';
import { Want } from '@kit.AbilityKit';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-HceManager';

/**
 * APDU 命令结构
 */
interface ApduCommand {
  cla: number;    // 类字节
  ins: number;    // 指令字节
  p1: number;     // 参数1
  p2: number;     // 参数2
  lc: number;     // 数据长度
  data: number[]; // 数据
  le: number;     // 期望返回数据长度
}

/**
 * HCE（Host Card Emulation）管理器
 * 负责处理所有与NFC卡模拟相关的功能
 */
@Observed
export class HceManager {
  private static instance: HceManager;

  @Track hasCapability: boolean = false;
  @Track isDefaultPaymentApp: boolean = false;
  @Track isSubscribe: boolean = false;

  private service: cardEmulation.HceService | null = null;
  private commandCallback: AsyncCallback<number[]> | null = null;
  private elementName: bundleManager.ElementName | null = null;
  private toForegroundCallback: (() => void) | null = null;

  // 支持的 AID 列表
  private readonly supportedAidList: string[] = [
    "A000000333010101",  // 中国工商银行
    "A000000632010105",  // 北京一卡通
    "A00000000386980701",  // 上海复旦微
    "A000000632010105535A4B5700000533",  // 淄博卡
    "4351515041592E5359533331",  // 重庆通卡
    "D1560001018003800000000100000011",  // 测试钱包卡1
    "D1560001018003800000000100000022",  // 测试钱包卡2
    "D1560001018003800000000100000033",  // 测试钱包卡3
    "D1560001018003800000000100000044",  // 测试钱包卡4
    "474D534D324D6F647532",  // 门禁卡
    "315041592E5359532E44444630310591",  // 福州卡
    "A0000006320101050113581058000000",  // 深圳通
    "A000000632010105535A4B5700000731",  // 长沙edep
    "A0000006320101053000300100083010",  // 南京通卡
    "A0000003330101020063485750415905"   // 上海Mot
  ];

  public static getInstance(): HceManager {
    if (!HceManager.instance) {
      HceManager.instance = new HceManager();
    }
    return HceManager.instance;
  }

  /**
   * 初始化 HCE 管理服务
   */
  public initialize(want: Want, callback: () => void): boolean {
    this.checkHceCapability();
    if (!this.hasCapability) {
      hilog.error(DOMAIN, TAG, '设备不支持 HCE 功能');
      return false;
    }

    // 根据应用程序信息，初始化 ElementName
    this.elementName = {
      bundleName: want.bundleName ?? '',
      abilityName: want.abilityName ?? '',
      moduleName: want.moduleName || "entry"
    };
    hilog.info(DOMAIN, TAG, 'ElementName initialized: %{public}s', JSON.stringify(this.elementName));

    this.toForegroundCallback = callback;

    this.checkDefaultPaymentApp();
    if (!this.isDefaultPaymentApp) {
      hilog.warn(DOMAIN, TAG, '未设置为默认支付应用');
    }

    if (canIUse('SystemCapability.Communication.NFC.CardEmulation')) {
      this.service = new cardEmulation.HceService();
      if (!this.service) {
        hilog.error(DOMAIN, TAG, 'HCE 服务初始化失败');
        return false;
      }
    } else {
      hilog.error(DOMAIN, TAG, '系统不支持 HCE 功能');
      return false;
    }

    this.commandCallback = (error: BusinessError, command: number[]) => {
      if (!error) {
        if (command == null || command == undefined) {
          hilog.error(DOMAIN, TAG, 'commandCb has invalid hceCommand.');
          return;
        }

        if (this.toForegroundCallback) {
          this.toForegroundCallback();
        }

        // 处理接收到的 APDU 命令
        this.handleHceCommand(command);
      } else {

      }
    };

    if (!this.commandCallback) {
      hilog.error(DOMAIN, TAG, 'commandCallback 未初始化，无法启动后台服务');
      return false;
    }

    return true;
  }

  /**
   * 检查 HCE 能力
   */
  public checkHceCapability(): void {
    this.hasCapability = false;
    try {
      if (canIUse('SystemCapability.Communication.NFC.CardEmulation')) {
        this.hasCapability = cardEmulation.hasHceCapability();
      }
    } catch (error) {
      hilog.error(DOMAIN, TAG, '检查HCE能力失败:', error?.message ?? '未知错误');
    }
  }

  /**
   * 检查是否为默认支付应用
   */
  public checkDefaultPaymentApp(): void {
    this.isDefaultPaymentApp = false;
    try {
      if (canIUse('SystemCapability.Communication.NFC.CardEmulation')) {
        this.isDefaultPaymentApp = cardEmulation.isDefaultService(this.elementName, cardEmulation.CardType.PAYMENT);
      }
    } catch (error) {
      hilog.error(DOMAIN, TAG, '检查默认支付应用失败: %{public}s', error?.message ?? '未知错误');
    }
  }

  /**
   * 请求设置为默认支付应用
   */
  public async requestDefaultPaymentApp(): Promise<boolean> {
    try {
      if (!this.hasCapability) {
        DialogHelper.showToast('设备不支持 NFC 支付功能');
        return false;
      }

      if (canIUse('SystemCapability.Communication.NFC.CardEmulation')) {
        hilog.info(DOMAIN, TAG, '请求设置为默认支付应用');
        await nfcTmsController.setDefaultPaymentService(this.elementName);
        return true;
      } else {
        DialogHelper.showToast('系统不支持设置默认支付应用');
        return false;
      }
    } catch (error) {
      hilog.error(DOMAIN, TAG, '请求设置默认支付应用失败: %{public}s', error?.message ?? '未知错误');
      DialogHelper.showToast('设置失败');
      return false;
    }
  }

  /**
   * 订阅 HCE 刷卡数据接收
   */
  public subscribeHce(): void {
    try {
      if (!this.service) {
        hilog.error(DOMAIN, TAG, 'HCE 服务未初始化，无法启动 HCE 刷卡数据接收服务');
        return;
      }

      if (canIUse('SystemCapability.Communication.NFC.CardEmulation')) {
        this.service.on('hceCmd', this.commandCallback);
        this.isSubscribe = true;
      } else {
        hilog.error(DOMAIN, TAG, '系统不支持 HCE 功能');
      }
    } catch (error) {
      hilog.error(DOMAIN, TAG, '启动 HCE 刷卡数据接收服务失败: %{public}s', error?.message ?? '未知错误');
    }
  }

  /**
   * 取消 HCE 刷卡数据接收
   */
  public unsubscribeHce(): void {
    try {
      if (!this.service) {
        hilog.error(DOMAIN, TAG, 'HCE 服务未初始化，无法停止');
        return;
      }

      if (!this.isSubscribe) {
        hilog.error(DOMAIN, TAG, '未订阅 HCE 刷卡数据接收服务，无需停止');
        return;
      }

      if (canIUse('SystemCapability.Communication.NFC.CardEmulation')) {
        this.service.stop(this.elementName);
        this.isSubscribe = false;
        hilog.info(DOMAIN, TAG, '成功取消 HCE 刷卡数据接收服务订阅');
      } else {
        hilog.error(DOMAIN, TAG, '系统不支持 HCE 功能');
      }
    } catch (error) {
      hilog.error(DOMAIN, TAG, '取消 HCE 刷卡数据接收订阅服务失败: %{public}s', error?.message ?? '未知错误');
    }
  }

  /**
   * 处理 HCE 命令
   */
  private handleHceCommand(command: number[]): void {
    hilog.info(DOMAIN, TAG, 'Received HCE command: %{public}s', JSON.stringify(command));

    try {
      // 解析 APDU 命令
      const apduCommand = this.parseApduCommand(command);

      // 根据命令生成响应
      const responseData = this.generateResponse(apduCommand);

      // 发送响应
      this.transmitResponse(responseData);
    } catch (error) {
      hilog.error(DOMAIN, TAG, 'Error handling HCE command: %{public}s', JSON.stringify(error));
      // 发送错误响应
      this.transmitResponse([0x6F, 0x00]); // 通用错误响应
    }
  }

  /**
   * 解析 APDU 命令
   */
  private parseApduCommand(command: number[]): ApduCommand {
    if (command.length < 4) {
      throw new Error('Invalid APDU command length');
    }

    return {
      cla: command[0],    // 类字节
      ins: command[1],    // 指令字节
      p1: command[2],     // 参数1
      p2: command[3],     // 参数2
      lc: command.length > 4 ? command[4] : 0,  // 数据长度
      data: command.length > 5 ? command.slice(5, 5 + (command[4] || 0)) : [],
      le: command.length > 5 + (command[4] || 0) ? command[command.length - 1] : 0
    };
  }

  /**
   * 生成响应数据
   */
  private generateResponse(apduCommand: ApduCommand): number[] {
    hilog.info(DOMAIN, TAG, 'Processing APDU - CLA: 0x%{public}s, INS: 0x%{public}s',
      apduCommand.cla.toString(16).padStart(2, '0').toUpperCase(),
      apduCommand.ins.toString(16).padStart(2, '0').toUpperCase());

    // 根据不同的指令类型生成响应
    switch (apduCommand.ins) {
      case 0xA4: // SELECT 命令
        return this.handleSelectCommand(apduCommand);
      case 0xB0: // READ BINARY 命令
        return this.handleReadBinaryCommand();
      case 0xD6: // UPDATE BINARY 命令
        return this.handleUpdateBinaryCommand();
      case 0x84: // GET CHALLENGE 命令
        return this.handleGetChallengeCommand(apduCommand);
      default:
        hilog.warn(DOMAIN, TAG, 'Unsupported instruction: 0x%{public}s',
          apduCommand.ins.toString(16).padStart(2, '0').toUpperCase());
        return [0x6D, 0x00]; // 指令不支持
    }
  }

  /**
   * 发送响应数据
   */
  private transmitResponse(responseData: number[]): void {
    if (!this.service) {
      hilog.error(DOMAIN, TAG, 'HCE 服务未启动');
      return;
    }

    if (canIUse('SystemCapability.Communication.NFC.CardEmulation')) {
      this.service.transmit(responseData).then(() => {
        hilog.info(DOMAIN, TAG, 'Response transmitted successfully: %{public}s', JSON.stringify(responseData));
      }).catch((err: BusinessError) => {
        hilog.error(DOMAIN, TAG, 'Failed to transmit response: %{public}s', JSON.stringify(err));
      });
    } else {
      hilog.error(DOMAIN, TAG, '系统不支持 HCE 功能');
    };
  }

  /**
   * 处理 SELECT 命令
   */
  private handleSelectCommand(apduCommand: ApduCommand): number[] {
    hilog.info(DOMAIN, TAG, 'Handling SELECT command');

    if (apduCommand.data.length > 0) {
      const aid = apduCommand.data.map(b => b.toString(16).padStart(2, '0').toUpperCase()).join('');
      hilog.info(DOMAIN, TAG, 'Selecting AID: %{public}s', aid);

      // 检查 AID 是否支持
      if (this.isSupportedAid(aid)) {
        // 返回成功响应，可以包含应用相关数据
        return [0x90, 0x00];
      } else {
        return [0x6A, 0x82]; // 文件未找到
      }
    }

    return [0x6A, 0x86]; // 参数错误
  }

  /**
   * 处理 READ BINARY 命令
   */
  private handleReadBinaryCommand(): number[] {
    hilog.info(DOMAIN, TAG, 'Handling READ BINARY command');

    // 模拟读取数据
    const mockData = [0x01, 0x02, 0x03, 0x04, 0x05];
    return [...mockData, 0x90, 0x00];
  }

  /**
   * 处理 UPDATE BINARY 命令
   */
  private handleUpdateBinaryCommand(): number[] {
    hilog.info(DOMAIN, TAG, 'Handling UPDATE BINARY command');

    // 模拟更新操作
    return [0x90, 0x00]; // 成功
  }

  /**
   * 处理 GET CHALLENGE 命令
   */
  private handleGetChallengeCommand(apduCommand: ApduCommand): number[] {
    hilog.info(DOMAIN, TAG, 'Handling GET CHALLENGE command');

    // 生成随机挑战数据
    const challengeLength = apduCommand.le || 8;
    const challenge: number[] = [];
    for (let i = 0; i < challengeLength; i++) {
      challenge.push(Math.floor(Math.random() * 256));
    }

    return [...challenge, 0x90, 0x00];
  }

  /**
   * 检查 AID 是否支持
   */
  private isSupportedAid(aid: string): boolean {
    return this.supportedAidList.includes(aid.toUpperCase());
  }

  /**
   * 测试方法：模拟HCE命令触发支付流程
   * 用于测试完整的支付流程
   */
  public simulatePaymentFlow(): void {
    hilog.info(DOMAIN, TAG, '开始模拟支付流程测试');

    try {
      // 模拟设置卡片数据
      const mockCardData = 'A000000333010101';
      AppStorage.setOrCreate('hceCardData', mockCardData);

      // 模拟HCE命令
      const mockCommand = [0x00, 0xA4, 0x04, 0x00, 0x08, 0xA0, 0x00, 0x00, 0x03, 0x33, 0x01, 0x01, 0x01];

      // 触发toForeground回调
      if (this.toForegroundCallback) {
        hilog.info(DOMAIN, TAG, '触发toForeground回调');
        this.toForegroundCallback();
      } else {
        hilog.error(DOMAIN, TAG, 'toForegroundCallback未设置');
      }

    } catch (error) {
      hilog.error(DOMAIN, TAG, '模拟支付流程失败: %{public}s', String(error));
    }
  }

}