import { hilog } from '@kit.PerformanceAnalysisKit';
import { Want } from '@kit.AbilityKit';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-PaymentFlowTest';

/**
 * 支付流程测试类
 * 用于测试从HCE命令触发到PaymentAbility启动再到支付界面弹出的完整流程
 */
export class PaymentFlowTest {

  /**
   * 模拟HCE命令触发支付流程
   */
  static simulateHcePaymentFlow(): void {
    hilog.info(DOMAIN, TAG, '开始模拟HCE支付流程测试');

    try {
      // 1. 模拟设置HCE卡片数据
      const mockCardData = 'A000000333010101'; // 模拟银行卡AID
      AppStorage.setOrCreate('hceCardData', mockCardData);
      hilog.info(DOMAIN, TAG, '步骤1: 设置HCE卡片数据完成');

      // 2. 模拟HCE命令接收
      const mockCommand = [0x00, 0xA4, 0x04, 0x00, 0x08, 0xA0, 0x00, 0x00, 0x03, 0x33, 0x01, 0x01, 0x01];
      hilog.info(DOMAIN, TAG, '步骤2: 模拟接收HCE命令: %{public}s', JSON.stringify(mockCommand));

      // 3. 模拟触发toForeground回调
      // 注意：这里需要通过HceManager实例来触发
      hilog.info(DOMAIN, TAG, '步骤3: 准备触发toForeground回调');

      // 4. 验证AppStorage数据设置
      setTimeout(() => {
        PaymentFlowTest.verifyAppStorageData();
      }, 500);

    } catch (error) {
      hilog.error(DOMAIN, TAG, '模拟HCE支付流程失败: %{public}s', String(error));
    }
  }

  /**
   * 验证AppStorage数据
   */
  static verifyAppStorageData(): void {
    hilog.info(DOMAIN, TAG, '验证AppStorage数据');

    const transactionId = AppStorage.get('transactionId') as string;
    const hceCardData = AppStorage.get('hceCardData') as string;
    const launchSource = AppStorage.get('launchSource') as string;
    const hceEventTrigger = AppStorage.get('hceEventTrigger') as string;

    hilog.info(DOMAIN, TAG, '交易ID: %{public}s', transactionId || '未设置');
    hilog.info(DOMAIN, TAG, 'HCE卡片数据: %{public}s', hceCardData || '未设置');
    hilog.info(DOMAIN, TAG, '启动源: %{public}s', launchSource || '未设置');
    hilog.info(DOMAIN, TAG, 'HCE事件触发器: %{public}s', hceEventTrigger || '未设置');

    // 验证必要数据是否存在
    if (transactionId && hceCardData && launchSource) {
      hilog.info(DOMAIN, TAG, '✓ AppStorage数据验证通过');
    } else {
      hilog.error(DOMAIN, TAG, '✗ AppStorage数据验证失败');
    }
  }

  /**
   * 测试PaymentAbility启动参数
   */
  static testPaymentAbilityLaunch(bundleName: string, moduleName: string = 'entry'): Want {
    const transactionId = `TEST_TXN_${Date.now()}`;
    const mockCardData = 'A000000333010101';

    const want: Want = {
      bundleName: bundleName,
      abilityName: 'PaymentAbility',
      moduleName: moduleName,
      parameters: {
        transactionId: transactionId,
        launchSource: 'HCE_COMMAND',
        hceCardData: mockCardData
      }
    };

    hilog.info(DOMAIN, TAG, '测试PaymentAbility启动参数: %{public}s', JSON.stringify(want));
    return want;
  }

  /**
   * 验证支付界面状态
   */
  static verifyPaymentUIState(): boolean {
    try {
      const transactionId = AppStorage.get('transactionId') as string;
      const hceEventTrigger = AppStorage.get('hceEventTrigger') as string;

      if (!transactionId) {
        hilog.error(DOMAIN, TAG, '✗ 支付界面验证失败: 缺少交易ID');
        return false;
      }

      hilog.info(DOMAIN, TAG, '✓ 支付界面状态验证通过');
      hilog.info(DOMAIN, TAG, '当前交易ID: %{public}s', transactionId);
      hilog.info(DOMAIN, TAG, '事件触发器: %{public}s', hceEventTrigger || '无');

      return true;
    } catch (error) {
      hilog.error(DOMAIN, TAG, '✗ 支付界面状态验证异常: %{public}s', String(error));
      return false;
    }
  }

  /**
   * 清理测试数据
   */
  static cleanupTestData(): void {
    hilog.info(DOMAIN, TAG, '清理测试数据');

    const keysToClean = ['transactionId', 'hceCardData', 'launchSource', 'hceEventTrigger'];
    keysToClean.forEach(key => {
      AppStorage.delete(key);
    });

    hilog.info(DOMAIN, TAG, '测试数据清理完成');
  }

  /**
   * 运行完整测试流程
   */
  static runFullTest(): void {
    hilog.info(DOMAIN, TAG, '=== 开始完整支付流程测试 ===');

    // 清理之前的数据
    PaymentFlowTest.cleanupTestData();

    // 运行模拟流程
    PaymentFlowTest.simulateHcePaymentFlow();

    // 延迟验证结果
    setTimeout(() => {
      const isValid = PaymentFlowTest.verifyPaymentUIState();
      if (isValid) {
        hilog.info(DOMAIN, TAG, '=== 支付流程测试通过 ===');
      } else {
        hilog.error(DOMAIN, TAG, '=== 支付流程测试失败 ===');
      }
    }, 1000);
  }
}
