# 支付流程实现文档

## 概述

本文档描述了通过 `toForeground` 拉起 `PaymentAbility` 后弹出支付界面的完整流程实现。

## 流程架构

```
HCE命令接收 → toForeground回调 → 启动PaymentAbility → 加载PaymentShowPage → 弹出支付确认对话框
```

## 核心组件

### 1. EntryAbility.toForeground()

**修改内容：**
- 改为启动 `PaymentAbility` 而不是 `EntryAbility`
- 生成交易ID并设置到 `AppStorage`
- 传递必要的参数给 `PaymentAbility`

**关键代码：**
```typescript
public toForeground(): void {
  const transactionId = this.generateTransactionId();
  const hceCardData = AppStorage.get('hceCardData') as string || '';
  
  AppStorage.setOrCreate('transactionId', transactionId);
  AppStorage.setOrCreate('hceCardData', hceCardData);
  AppStorage.setOrCreate('launchSource', 'HCE_COMMAND');

  const want: Want = {
    bundleName: this.context.abilityInfo.bundleName,
    abilityName: 'PaymentAbility',
    moduleName: this.context.abilityInfo.moduleName || "entry",
    parameters: {
      transactionId: transactionId,
      launchSource: 'HCE_COMMAND',
      hceCardData: hceCardData
    }
  };

  this.context.startAbility(want);
}
```

### 2. PaymentAbility 生命周期

**新增方法：**
- `onForeground()`: 当PaymentAbility被拉起到前台时调用
- `ensurePaymentUIVisible()`: 确保支付界面可见

**关键代码：**
```typescript
onForeground(): void {
  hilog.info(DOMAIN, TAG, 'PaymentAbility onForeground');
  this.loadDataFromAppStorage();
  this.ensurePaymentUIVisible();
}

private ensurePaymentUIVisible(): void {
  setTimeout(() => {
    const transactionId = AppStorage.get('transactionId') as string;
    if (transactionId) {
      AppStorage.setOrCreate('hceEventTrigger', Date.now().toString());
    }
  }, 100);
}
```

### 3. PaymentShowPage 界面优化

**改进内容：**
- 增强 `aboutToAppear()` 方法，支持延迟加载
- 优化 `onHceEventTriggerChanged()` 事件监听
- 新增 `onPageShow()` 页面显示监听

**关键代码：**
```typescript
aboutToAppear(): void {
  this.loadTransactionData();
  
  if (this.transactionData.transactionId) {
    this.showConfirmDialog = true;
  } else {
    // 延迟检查处理异步加载情况
    setTimeout(() => {
      this.loadTransactionData();
      if (this.transactionData.transactionId) {
        this.showConfirmDialog = true;
      }
    }, 200);
  }
}

onPageShow(): void {
  this.loadTransactionData();
  if (this.transactionData.transactionId && !this.showConfirmDialog) {
    this.showConfirmDialog = true;
  }
}
```

## 数据流转

### AppStorage 数据结构

| 键名 | 类型 | 描述 |
|------|------|------|
| `transactionId` | string | 交易ID，用于标识本次支付 |
| `hceCardData` | string | HCE卡片数据 |
| `launchSource` | string | 启动源，固定为 'HCE_COMMAND' |
| `hceEventTrigger` | string | 事件触发器，用于通知界面更新 |

### 数据传递流程

1. **HCE命令接收** → 设置 `hceCardData`
2. **toForeground调用** → 生成 `transactionId`，设置 `launchSource`
3. **PaymentAbility启动** → 接收Want参数，更新AppStorage
4. **PaymentShowPage加载** → 读取AppStorage数据，显示支付界面

## 测试功能

### PaymentFlowTest 测试类

提供完整的支付流程测试功能：

```typescript
// 运行完整测试
PaymentFlowTest.runFullTest();

// 模拟HCE支付流程
PaymentFlowTest.simulateHcePaymentFlow();

// 验证AppStorage数据
PaymentFlowTest.verifyAppStorageData();

// 验证支付界面状态
PaymentFlowTest.verifyPaymentUIState();
```

### 在MainPage中测试

在主页面的导航菜单中添加了测试按钮（播放图标），点击可以触发支付流程测试。

## 使用方法

### 1. 正常使用流程

1. 应用启动后，HCE服务自动初始化
2. 当NFC设备靠近时，触发HCE命令
3. HCE命令处理完成后，自动调用 `toForeground()`
4. `PaymentAbility` 启动并显示支付确认界面
5. 用户确认或取消支付

### 2. 测试流程

1. 打开应用主页面
2. 点击右上角的播放图标（测试按钮）
3. 观察日志输出，验证流程是否正确
4. 应该能看到 `PaymentAbility` 启动并显示支付界面

## 注意事项

1. **权限要求**: 确保应用具有 `ohos.permission.START_ABILITIES_FROM_BACKGROUND` 权限
2. **模块配置**: 确保 `PaymentAbility` 在 `module.json5` 中正确配置
3. **数据清理**: 支付完成后会自动清理AppStorage中的临时数据
4. **错误处理**: 如果启动PaymentAbility失败，会回退到启动主应用

## 故障排除

### 常见问题

1. **PaymentAbility启动失败**
   - 检查module.json5配置
   - 确认权限设置
   - 查看错误日志

2. **支付界面不显示**
   - 检查AppStorage数据是否正确设置
   - 确认hceEventTrigger是否触发
   - 查看PaymentShowPage日志

3. **数据传递异常**
   - 验证Want参数是否正确
   - 检查AppStorage读写操作
   - 确认数据格式正确

### 调试建议

1. 开启详细日志输出
2. 使用测试功能验证流程
3. 检查各个生命周期方法的调用顺序
4. 验证AppStorage数据的完整性
