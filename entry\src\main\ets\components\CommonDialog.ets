import promptAction from '@ohos.promptAction';

export interface DialogOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmColor?: string;
  cancelColor?: string;
}

export interface ToastOptions {
  message: string;
  duration?: number;
  bottom?: string | number;
}



interface ShowToastParams {
  message: string;
  duration: number;
  bottom?: string | number;
}

export class DialogHelper {
  // 默认配置
  private static readonly DEFAULT_TOAST_DURATION = 2000;

  /**
   * 显示确认对话框
   * @param options 对话框选项
   * @returns Promise<boolean> 用户选择结果，true 表示点击确认按钮，false 表示点击取消按钮
   */
  static async showConfirmDialog(options: DialogOptions): Promise<boolean> {
    try {
      const result = await promptAction.showDialog({
        title: options.title,
        message: options.message,
        buttons: [
          {
            text: '确定',
            color: '#FF0000'  // 删除操作用红色
          }
        ]
      });

      // 我们的确定按钮是 index 0
      return result.index === 0;
    } catch (error) {
      console.error('显示对话框失败:', error);
      return false;  // 发生错误时返回 false，相当于取消操作
    }
  }

  /**
   * 显示 Toast 消息
   * @param options Toast 选项或消息字符串
   * @returns void
   */
  static showToast(options: ToastOptions | string): void {
    try {
      let toastParams: ShowToastParams;

      if (typeof options === 'string') {
        toastParams = {
          message: options,
          duration: DialogHelper.DEFAULT_TOAST_DURATION
        };
      } else {
        toastParams = {
          message: options.message,
          duration: options.duration || DialogHelper.DEFAULT_TOAST_DURATION,
          bottom: options.bottom
        };
      }

      promptAction.showToast(toastParams);
    } catch (error) {
      console.error('显示 Toast 失败:', error);
    }
  }
}