import { AbilityConstant, ConfigurationConstant, UIAbility, Want } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';

import { HceManager } from '../models/HceManager';
import { ScriptManager } from '../models/ScriptManager';
import { CardManager } from '../models/CardManager';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-Entry';

export default class EntryAbility extends UIAbility {
  private hceManager: HceManager = HceManager.getInstance();
  private scriptManager: ScriptManager = ScriptManager.getInstance();
  private cardManager: CardManager = CardManager.getInstance();

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onCreate');

    // 初始化ScriptManager
    this.scriptManager.initialize(this.context);

    // 检查设备是否支持NFC能力和HCE能力
    if (!canIUse("SystemCapability.Communication.NFC.Core")) {
      hilog.error(DOMAIN, TAG, 'NFC功能不可用');
      return;
    }

    if (!this.hceManager.initialize(want, () => this.toForeground())) {
      hilog.error(DOMAIN, TAG, 'HCE功能不可用');
      return;
    }

    this.hceManager.subscribeHce();
    if (!this.hceManager.isSubscribe) {
      hilog.error(DOMAIN, TAG, '启动HCE后台刷卡服务失败');
      return;
    }
  }

  onDestroy(): void {
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onDestroy');

    // 停止HCE服务
    this.hceManager.unsubscribeHce();
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onWindowStageCreate');

    // 加载卡片列表
    this.cardManager.loadCardsFromStorage(this.context);

    windowStage.loadContent('pages/MainPage', (err) => {
      if (err.code) {
        hilog.error(DOMAIN, TAG, 'Failed to load the content. Cause: %{public}s', JSON.stringify(err));
        return;
      }
      hilog.info(DOMAIN, TAG, 'Succeeded in loading the content.');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onBackground');
  }

  /**
   * 将应用从后台切换到前台，启动PaymentAbility显示支付界面
   */
  public toForeground(): void {
    try {
      // 生成交易ID
      const transactionId = this.generateTransactionId();

      // 获取当前的HCE卡片数据
      const hceCardData = AppStorage.get('hceCardData') as string || '';

      // 设置AppStorage数据供PaymentAbility使用
      AppStorage.setOrCreate('transactionId', transactionId);
      AppStorage.setOrCreate('hceCardData', hceCardData);
      AppStorage.setOrCreate('launchSource', 'HCE_COMMAND');



      const want: Want = {
        bundleName: this.context.abilityInfo.bundleName,
        abilityName: 'PaymentAbility',
        moduleName: this.context.abilityInfo.moduleName || "entry",
        parameters: {
          transactionId: transactionId,
          launchSource: 'HCE_COMMAND',
          hceCardData: hceCardData
        }
      };

      hilog.info(DOMAIN, TAG, '正在启动PaymentAbility，交易ID: %{public}s', transactionId);

      this.context.startAbility(want).then(() => {
        hilog.info(DOMAIN, TAG, 'PaymentAbility启动成功');
      }).catch((err: BusinessError) => {
        hilog.error(DOMAIN, TAG, '启动PaymentAbility失败: %{public}s', JSON.stringify(err));
      });
    } catch (error) {
      hilog.error(DOMAIN, TAG, '启动PaymentAbility时发生异常: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 生成交易ID
   */
  private generateTransactionId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `TXN_${timestamp}_${random}`;
  }



}