import { HceManager } from '../models/HceManager';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { NavigationUtils } from '../models/NavigationManager';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-HceManagerSubPage';

@Component
export struct HceManagerSubPage {
  @State hceManager: HceManager = HceManager.getInstance();
  @State hasHceCapability: boolean = false;
  @State isDefaultPaymentApp: boolean = false;
  @State isBackgroundServiceActive: boolean = false;

  aboutToAppear(): void {
    this.updateStatus();
  }

  private updateStatus(): void {
    this.hasHceCapability = this.hceManager.hasCapability;
    this.isDefaultPaymentApp = this.hceManager.isDefaultPaymentApp;
    this.isBackgroundServiceActive = this.hceManager.isSubscribe;
  }

  build() {
    NavDestination() {
      Column() {
        // 内容区域
        Scroll() {
          Column() {
            // 功能状态卡片
            Column() {
              Text('功能状态')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 20 })
                .fontColor($r('sys.color.font_primary'))

              // HCE 能力状态
              Row() {
                Text('HCE 功能支持')
                  .fontSize(16)
                  .fontColor($r('sys.color.font_primary'))
                  .layoutWeight(1)

                Text(this.hasHceCapability ? '支持' : '不支持')
                  .fontSize(16)
                  .fontColor(this.hasHceCapability ? '#4CAF50' : '#F44336')
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .height(48)
              .padding({ left: 16, right: 16 })
              .margin({ bottom: 8 })
              .backgroundColor($r('sys.color.background_secondary'))
              .borderRadius(12)
              .alignItems(VerticalAlign.Center)

              // 默认支付应用状态
              Row() {
                Text('默认支付应用')
                  .fontSize(16)
                  .fontColor($r('sys.color.font_primary'))
                  .layoutWeight(1)

                Text(this.isDefaultPaymentApp ? '是' : '否')
                  .fontSize(16)
                  .fontColor(this.isDefaultPaymentApp ? '#4CAF50' : '#FF9800')
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .height(48)
              .padding({ left: 16, right: 16 })
              .margin({ bottom: 8 })
              .backgroundColor($r('sys.color.background_secondary'))
              .borderRadius(12)
              .alignItems(VerticalAlign.Center)

              // 刷卡服务状态
              Row() {
                Text('刷卡服务')
                  .fontSize(16)
                  .fontColor($r('sys.color.font_primary'))
                  .layoutWeight(1)

                Text(this.isBackgroundServiceActive ? '运行中' : '已停止')
                  .fontSize(16)
                  .fontColor(this.isBackgroundServiceActive ? '#4CAF50' : '#9E9E9E')
                  .fontWeight(FontWeight.Medium)
              }
              .width('100%')
              .height(48)
              .padding({ left: 16, right: 16 })
              .backgroundColor($r('sys.color.background_secondary'))
              .borderRadius(12)
              .alignItems(VerticalAlign.Center)
            }
            .width('100%')
            .padding(20)
            .backgroundColor(Color.White)
            .borderRadius(16)
            .margin({ top: 20, left: 16, right: 16 })
            .shadow({
              radius: 8,
              color: 'rgba(0, 0, 0, 0.08)',
              offsetX: 0,
              offsetY: 2
            })

            // 服务控制卡片
            Column() {
              Text('服务控制')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 20 })
                .fontColor($r('sys.color.font_primary'))

              Button('订阅刷卡数据接收')
                .width('100%')
                .height(48)
                .backgroundColor(this.hasHceCapability && !this.isBackgroundServiceActive ? '#007AFF' : '#CCCCCC')
                .borderRadius(12)
                .fontColor(Color.White)
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .enabled(this.hasHceCapability && !this.isBackgroundServiceActive)
                .margin({ bottom: 12 })
                .onClick(() => {
                  this.hceManager.subscribeHce();
                  hilog.info(DOMAIN, TAG, '启动刷卡数据接收服务');
                  this.updateStatus();
                })

              Button('取消订阅刷卡数据接收')
                .width('100%')
                .height(48)
                .backgroundColor(this.isBackgroundServiceActive ? '#FF3B30' : '#CCCCCC')
                .borderRadius(12)
                .fontColor(Color.White)
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .enabled(this.isBackgroundServiceActive)
                .margin({ bottom: 12 })
                .onClick(() => {
                  this.hceManager.unsubscribeHce();
                  hilog.info(DOMAIN, TAG, '停止刷卡数据接收服务');
                  this.updateStatus();
                })

              Button('刷新状态')
                .width('100%')
                .height(48)
                .backgroundColor('#8E8E93')
                .borderRadius(12)
                .fontColor(Color.White)
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .onClick(() => {
                  this.hceManager.checkHceCapability();
                  this.hceManager.checkDefaultPaymentApp();
                  this.updateStatus();
                })
            }
            .width('100%')
            .padding(20)
            .backgroundColor(Color.White)
            .borderRadius(16)
            .margin({ top: 16, left: 16, right: 16 })
            .shadow({
              radius: 8,
              color: 'rgba(0, 0, 0, 0.08)',
              offsetX: 0,
              offsetY: 2
            })

            // 使用说明卡片
            Column() {
              Text('使用说明')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 16 })
                .fontColor($r('sys.color.font_primary'))

              Column({ space: 8 }) {
                Row() {
                  Text('•')
                    .fontSize(14)
                    .fontColor($r('sys.color.font_secondary'))
                    .width(16)
                  Text('刷卡服务允许应用接收刷卡数据')
                    .fontSize(14)
                    .fontColor($r('sys.color.font_secondary'))
                    .layoutWeight(1)
                }
                .width('100%')
                .alignItems(VerticalAlign.Top)

                Row() {
                  Text('•')
                    .fontSize(14)
                    .fontColor($r('sys.color.font_secondary'))
                    .width(16)
                  Text('需要设备支持NFC功能并开启NFC')
                    .fontSize(14)
                    .fontColor($r('sys.color.font_secondary'))
                    .layoutWeight(1)
                }
                .width('100%')
                .alignItems(VerticalAlign.Top)

                Row() {
                  Text('•')
                    .fontSize(14)
                    .fontColor($r('sys.color.font_secondary'))
                    .width(16)
                  Text('建议设置为默认支付应用以获得最佳体验')
                    .fontSize(14)
                    .fontColor($r('sys.color.font_secondary'))
                    .layoutWeight(1)
                }
                .width('100%')
                .alignItems(VerticalAlign.Top)
              }
            }
            .width('100%')
            .padding(20)
            .backgroundColor(Color.White)
            .borderRadius(16)
            .margin({ top: 16, left: 16, right: 16 })
            .shadow({
              radius: 8,
              color: 'rgba(0, 0, 0, 0.08)',
              offsetX: 0,
              offsetY: 2
            })
          }
          .width('100%')
          .padding({ top: 0, bottom: 40 })
        }
        .layoutWeight(1)
        .scrollBar(BarState.Auto)
        .edgeEffect(EdgeEffect.Spring)
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#F8F8F8')
    }
    .title('HCE管理')
    .onBackPressed(() => {
      NavigationUtils.pop();
      return true;
    })
    .onReady(() => {
      hilog.info(DOMAIN, TAG, 'HCE管理页面已准备就绪');
    })
  }
}

export function HceManagerPageBuilder() {
  HceManagerSubPage()
}
