import { hilog } from '@kit.PerformanceAnalysisKit';
import { Context } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { fileIo as fs } from '@kit.CoreFileKit';
import { util } from '@kit.ArkTS';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-ScriptManager';

/**
 * 脚本管理器 - 用于管理script文件
 */
@Observed
export class ScriptManager {
  private static instance: ScriptManager;
  private context: Context | null = null;
  private sandboxScriptPath: string = '';
  @Track cardIdList: string[] = [];

  public static getInstance(): ScriptManager {
    if (!ScriptManager.instance) {
      ScriptManager.instance = new ScriptManager();
    }
    return ScriptManager.instance;
  }

  /**
   * 初始化ScriptManager，设置上下文
   * @param context 应用上下文
   */
  public initialize(context: Context): void {
    this.context = context;
    this.sandboxScriptPath = context.filesDir + '/script';

    try {
      if (!fs.accessSync(this.sandboxScriptPath)) {
        fs.mkdirSync(this.sandboxScriptPath, true);
        hilog.info(DOMAIN, TAG, '创建沙箱脚本目录: %{public}s', this.sandboxScriptPath);
      }
    } catch (err) {
      fs.mkdirSync(this.sandboxScriptPath, true);
      hilog.info(DOMAIN, TAG, '创建沙箱脚本目录: %{public}s', this.sandboxScriptPath);
    }

    hilog.info(DOMAIN, TAG, 'ScriptManager初始化完成，沙箱路径: %{public}s', this.sandboxScriptPath);
  }

  /**
   * 异步获取卡片ID列表
   */
  public async loadCardIdList(): Promise<void> {
    try {
      this.cardIdList = []; // 先清空现有列表

      if (!this.context) {
        hilog.error(DOMAIN, TAG, 'ScriptManager未初始化，无法获取卡片ID列表');
        return;
      }

      // 获取script文件夹中的文件列表
      const scriptFiles = await this.context.resourceManager.getRawFileList('script');

      // 筛选-Add.ts文件并提取卡片ID
      this.cardIdList = scriptFiles
        .filter(filename => filename.endsWith('-Add.ts'))
        .map(filename => filename.replace('-Add.ts', ''))
        .filter(id => id.length > 0);

      hilog.info(DOMAIN, TAG, '异步获取卡片ID列表成功: %{public}s', JSON.stringify(this.cardIdList));
    } catch (error) {
      const err: BusinessError = error as BusinessError;
      hilog.error(DOMAIN, TAG, '异步获取卡片ID列表失败: %{public}s', err.message);
      this.cardIdList = []; // 出错时清空列表
    }
  }

  /**
   * 获取当前的卡片ID列表（同步方法，用于UI绑定）
   */
  public getCardIdList(): string[] {
    return [...this.cardIdList];
  }

  /**
   * 读取指定卡片的脚本文件内容
   * 基于参考代码实现，支持从资源文件和沙箱目录读取
   * @param cardId 卡片ID
   * @param scriptType 脚本类型
   * @returns 脚本内容或null
   */
  public async readScriptFile(cardId: string, scriptType: string): Promise<string | null> {
    try {
      if (!this.context) {
        hilog.error(DOMAIN, TAG, 'ScriptManager未初始化，无法读取脚本文件');
        return null;
      }

      // 构建文件名，处理特殊情况
      let fileName: string;
      if (scriptType === '' || scriptType === null || scriptType === undefined) {
        // 对于没有脚本类型的文件（如Utils.ts）
        fileName = `${cardId}.ts`;
      } else {
        // 标准格式：cardId-scriptType.ts
        fileName = `${cardId}-${scriptType}.ts`;
      }

      // 从资源文件读取
      try {
        const scriptContent = await this.context.resourceManager.getRawFileContent(`script/${fileName}`);

        // 将Uint8Array转换为字符串
        const textDecoderOptions: util.TextDecoderOptions = {
          fatal: false,
          ignoreBOM: true
        };
        const decodeToStringOptions: util.DecodeToStringOptions = {
          stream: false
        };
        const textDecoder = util.TextDecoder.create('utf-8', textDecoderOptions);
        const scriptText = textDecoder.decodeToString(scriptContent, decodeToStringOptions);

        hilog.info(DOMAIN, TAG, '从资源文件成功读取脚本文件: %{public}s', fileName);
        return scriptText;
      } catch (resourceError) {
        hilog.warn(DOMAIN, TAG, '从资源文件读取脚本文件失败: %{public}s', (resourceError as BusinessError).message);
      }

      hilog.warn(DOMAIN, TAG, '脚本文件不存在: %{public}s', fileName);
      return null;
    } catch (error) {
      const err: BusinessError = error as BusinessError;
      hilog.error(DOMAIN, TAG, '读取脚本文件失败 %{public}s-%{public}s: %{public}s', cardId, scriptType, err.message);
      return null;
    }
  }
}
