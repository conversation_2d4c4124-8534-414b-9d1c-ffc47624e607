{"string": [{"name": "module_desc", "value": "TmsWallet smart card wallet"}, {"name": "EntryAbility_desc", "value": "Card Wallet"}, {"name": "EntryAbility_label", "value": "TmsWallet"}, {"name": "card_emulation_reason", "value": "Card Emulation"}, {"name": "start_abilities_from_background_reason", "value": "Start Abilities Background"}, {"name": "PaymentAbility_desc", "value": "Payment Processing"}, {"name": "PaymentAbility_label", "value": "Payment"}, {"name": "notification_reason", "value": "Send payment notifications"}]}