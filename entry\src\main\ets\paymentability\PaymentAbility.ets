import { UIAbility, AbilityConstant, Want } from '@kit.AbilityKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';
import { BusinessError } from '@kit.BasicServicesKit';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-Payment';

export default class PaymentAbility extends UIAbility {
  private autoCloseTimer: number | null = null;

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(DOMAIN, TAG, 'PaymentAbility onCreate');
    hilog.info(DOMAIN, TAG, 'Want: %{public}s', JSON.stringify(want));
    hilog.info(DOMAIN, TAG, 'LaunchParam: %{public}s', JSON.stringify(launchParam));

    // 存储启动参数到基类的launchWant属性
    this.launchWant = want;

    // 处理启动参数
    if (want.parameters) {
      const transactionId = want.parameters['transactionId'] as string;
      const launchSource = want.parameters['launchSource'] as string;

      if (transactionId) {
        AppStorage.setOrCreate('transactionId', transactionId);
        hilog.info(DOMAIN, TAG, 'Set transactionId from parameters: %{public}s', transactionId);
      }

      if (launchSource) {
        AppStorage.setOrCreate('launchSource', launchSource);
        hilog.info(DOMAIN, TAG, 'Set launchSource from parameters: %{public}s', launchSource);
      }
    }

    this.loadDataFromAppStorage();
  }

  onDestroy(): void {
    hilog.info(DOMAIN, TAG, 'Ability onDestroy');
    this.clearAutoCloseTimer();
  }

  onForeground(): void {
    hilog.info(DOMAIN, TAG, 'PaymentAbility onForeground');

    // 当PaymentAbility被拉起到前台时，重新加载数据并触发界面更新
    this.loadDataFromAppStorage();

    // 确保支付界面能够正确显示
    this.ensurePaymentUIVisible();

    // 设置自动关闭定时器，防止长时间占用资源
    this.setAutoCloseTimer();
  }

  onBackground(): void {
    hilog.info(DOMAIN, TAG, 'PaymentAbility onBackground');
  }

  /**
   * 确保支付界面可见
   */
  private ensurePaymentUIVisible(): void {
    try {
      // 延迟一小段时间确保界面已经加载完成
      setTimeout(() => {
        const transactionId = AppStorage.get('transactionId') as string;
        if (transactionId) {
          // 再次触发事件更新，确保PaymentShowPage能够显示对话框
          AppStorage.setOrCreate('hceEventTrigger', Date.now().toString());
          hilog.info(DOMAIN, TAG, 'Payment UI visibility ensured for transaction: %{public}s', transactionId);
        }
      }, 100);
    } catch (error) {
      hilog.error(DOMAIN, TAG, 'Error ensuring payment UI visibility: %{public}s', String(error));
    }
  }

  /**
   * 从 AppStorage 加载数据并触发事件更新
   */
  private loadDataFromAppStorage(): void {
    try {
      const transactionId = AppStorage.get('transactionId') as string;
      const hceCardData = AppStorage.get('hceCardData') as string;
      const launchSource = AppStorage.get('launchSource') as string;

      // 记录加载的数据
      if (transactionId) {
        hilog.info(DOMAIN, TAG, 'Loaded transactionId: %{public}s', transactionId);
      }
      if (hceCardData) {
        hilog.info(DOMAIN, TAG, 'Loaded hceCardData');
      }
      if (launchSource) {
        hilog.info(DOMAIN, TAG, 'Loaded launchSource: %{public}s', launchSource);
      }

      // 触发事件更新
      AppStorage.setOrCreate('hceEventTrigger', Date.now().toString());

    } catch (error) {
      hilog.error(DOMAIN, TAG, 'Failed to load AppStorage data: %{public}s', String(error));
    }
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    hilog.info(DOMAIN, TAG, 'Ability onWindowStageCreate');

    windowStage.loadContent('pages/PaymentShowPage', (err: BusinessError | null) => {
      if (err?.code) {
        hilog.error(DOMAIN, TAG, 'Failed to load payment page: %{public}s', err.message || 'Unknown error');
        return;
      }
      hilog.info(DOMAIN, TAG, 'Payment page loaded successfully');
    });
  }

  onWindowStageDestroy(): void {
    hilog.info(DOMAIN, TAG, 'Ability onWindowStageDestroy');
    this.clearAutoCloseTimer();
  }

  /**
   * 设置自动关闭定时器
   * 15秒后自动关闭PaymentAbility，避免长时间占用资源
   */
  private setAutoCloseTimer(): void {
    this.clearAutoCloseTimer();

    this.autoCloseTimer = setTimeout(() => {
      hilog.info(DOMAIN, TAG, 'Auto closing PaymentAbility due to timeout');
      this.context.terminateSelf();
    }, 15000); // 15秒后自动关闭，给用户足够时间操作
  }

  /**
   * 清除自动关闭定时器
   */
  private clearAutoCloseTimer(): void {
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
  }

  /**
   * 手动关闭PaymentAbility
   * 在支付完成或取消后调用
   */
  public closePaymentAbility(): void {
    hilog.info(DOMAIN, TAG, 'Manually closing PaymentAbility');
    this.clearAutoCloseTimer();
    this.context.terminateSelf();
  }
}