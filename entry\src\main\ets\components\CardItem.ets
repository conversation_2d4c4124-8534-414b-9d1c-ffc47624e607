import { CardModel } from '../models/CardManager';

@Component
export struct CardItem {
  @ObjectLink card: CardModel;
  @State isPressed: boolean = false;
  onSelect: (cardId: string) => void = () => {};
  onManage: (cardId: string) => void = () => {};

  // 主题色配置
  private readonly selectedBgColor = '#E8F5FE';
  private readonly normalBgColor = '#F5F5F5';
  private readonly selectedBorderColor = '#1698E8';
  private readonly normalBorderColor = '#E5E5E5';
  private readonly cardHeight = 120;

  build() {
    Column() {
      // 卡片主体内容
      Row() {
        Column({ space: 8 }) {
          // 卡片名称
          Text(this.card.name)
            .fontSize(20)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1A1A1A')
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .alignSelf(ItemAlign.Start)

          // 卡片类型
          Text(this.card.cardType)
            .fontSize(16)
            .fontColor('#666666')
            .opacity(0.8)
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .alignSelf(ItemAlign.Start)
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)


      }
      .width('100%')
      .height('100%')
      .padding({ left: 16, right: 16, top: 16, bottom: 16 })
      .justifyContent(FlexAlign.Start)
      .alignItems(VerticalAlign.Center)
    }
    .width('100%')
    .height(this.cardHeight)
    .backgroundColor(this.card.isSelected ? this.selectedBgColor : this.normalBgColor)
    .borderRadius(16)
    .border({
      width: this.card.isSelected ? 2 : 1,
      color: this.card.isSelected ? this.selectedBorderColor : this.normalBorderColor
    })
    .shadow({
      radius: this.card.isSelected ? 8 : 4,
      color: this.card.isSelected ? '#1698E820' : '#00000010',
      offsetX: 0,
      offsetY: 2
    })
    .margin({ top: 12 })
    .scale({ x: this.isPressed ? 0.95 : 1.0, y: this.isPressed ? 0.95 : 1.0 })
    .animation({
      duration: 200,
      curve: Curve.EaseInOut
    })
    .onClick(() => {
      this.onSelect(this.card.id);
    })
    .onTouch((event?: TouchEvent) => {
      if (event?.type === TouchType.Down) {
        // 添加按压效果
        this.animatePress(true);
      } else if (event?.type === TouchType.Up || event?.type === TouchType.Cancel) {
        this.animatePress(false);
      }
    })
  }

  // 按压动画效果
  private animatePress(pressed: boolean) {
    animateTo({
      duration: 150,
      curve: Curve.EaseInOut
    }, () => {
      this.isPressed = pressed;
    });
  }
}