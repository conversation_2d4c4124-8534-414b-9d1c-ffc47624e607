{"app": {"signingConfigs": [{"name": "default", "material": {"certpath": "C:/Users/<USER>/.ohos/config/openharmony/default_TmsWallet_Fw6xl-d1L8LpHT3nIKC01E9PZvI5e8fVByVT7o8T8OE=.cer", "keyAlias": "debugKey", "keyPassword": "0000001BACCC44C28A10AA0CD15E9011756619EA34DF04D4CA93E3D2D69F3C35FF1C1660916AE27E5683B2", "profile": "C:/Users/<USER>/.ohos/config/openharmony/default_TmsWallet_Fw6xl-d1L8LpHT3nIKC01E9PZvI5e8fVByVT7o8T8OE=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "C:/Users/<USER>/.ohos/config/openharmony/default_TmsWallet_Fw6xl-d1L8LpHT3nIKC01E9PZvI5e8fVByVT7o8T8OE=.p12", "storePassword": "0000001BE7229DBDC3994301B370F632755F36F21BC1046554E5C58265ED3E0AD3773BEBB72991D9A5634A"}}], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}}}], "buildModeSet": [{"name": "debug"}, {"name": "release"}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}