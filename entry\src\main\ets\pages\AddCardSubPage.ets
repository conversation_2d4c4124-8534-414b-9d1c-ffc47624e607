import promptAction from '@ohos.promptAction';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { NavigationUtils } from '../models/NavigationManager';
import { CardManager, CardModel } from '../models/CardManager';
import { ScriptManager } from '../models/ScriptManager';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-AddCardSubPage';

@Builder
export function AddCardPageBuilder(scriptManager: ScriptManager) {
  AddCardSubPage({ scriptManager: scriptManager })
}

@Component
struct AddCardSubPage {
  @State cardName: string = '';
  @State cardType: string = '复制卡';
  @State selectedCardId: string = '';
  @State isCardIdDropdownOpen: boolean = false;
  @StorageLink('cardManager') private cardManager: CardManager = CardManager.getInstance();
  @ObjectLink scriptManager: ScriptManager;

  async aboutToAppear(): Promise<void> {
    hilog.info(DOMAIN, TAG, 'aboutToAppear - 添加卡片子页面初始化');
    this.selectedCardId = '请选择卡片ID';
    // 加载卡片ID列表，UI会自动更新
    await this.scriptManager.loadCardIdList();
  }

  build() {
    NavDestination() {
      Column() {
        // 内容区域
        Scroll() {
          Column() {
            // 卡片类型选择
            Column() {
              Text('选择卡片类型')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 20 })
                .fontColor($r('sys.color.font_primary'))

              Row() {
                Radio({ value: '复制卡', group: 'cardType' })
                  .checked(this.cardType === '复制卡')
                  .onChange((isChecked: boolean) => {
                    if (isChecked) {
                      this.cardType = '复制卡';
                    }
                  })
                Text('复制卡')
                  .fontSize(16)
                  .margin({ left: 12 })
                  .fontColor($r('sys.color.font_primary'))
              }
              .width('100%')
              .height(48)
              .padding({ left: 16, right: 16 })
              .margin({ bottom: 8 })
              .backgroundColor(this.cardType === '复制卡' ? $r('sys.color.background_secondary') : Color.Transparent)
              .borderRadius(12)
              .alignItems(VerticalAlign.Center)
              .onClick(() => {
                this.cardType = '复制卡';
              })

              Row() {
                Radio({ value: '添加卡', group: 'cardType' })
                  .checked(this.cardType === '添加卡')
                  .onChange((isChecked: boolean) => {
                    if (isChecked) {
                      this.cardType = '添加卡';
                    }
                  })
                Text('添加卡')
                  .fontSize(16)
                  .margin({ left: 12 })
                  .fontColor($r('sys.color.font_primary'))
              }
              .width('100%')
              .height(48)
              .padding({ left: 16, right: 16 })
              .backgroundColor(this.cardType === '添加卡' ? $r('sys.color.background_secondary') : Color.Transparent)
              .borderRadius(12)
              .alignItems(VerticalAlign.Center)
              .onClick(() => {
                this.cardType = '添加卡';
              })
            }
            .width('100%')
            .padding(20)
            .backgroundColor(Color.White)
            .borderRadius(16)
            .margin({ top: 20, left: 16, right: 16 })
            .shadow({
              radius: 8,
              color: 'rgba(0, 0, 0, 0.08)',
              offsetX: 0,
              offsetY: 2
            })

            // 卡片名称输入
            Column() {
              Text('卡片名称')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 20 })
                .fontColor($r('sys.color.font_primary'))

              TextInput({ placeholder: '请输入卡片名称' })
                .width('100%')
                .height(48)
                .borderRadius(12)
                .backgroundColor($r('sys.color.background_secondary'))
                .fontSize(16)
                .padding({ left: 16, right: 16 })
                .onChange((value: string) => {
                  this.cardName = value;
                })
            }
            .width('100%')
            .padding(20)
            .backgroundColor(Color.White)
            .borderRadius(16)
            .margin({ top: 16, left: 16, right: 16 })
            .shadow({
              radius: 8,
              color: 'rgba(0, 0, 0, 0.08)',
              offsetX: 0,
              offsetY: 2
            })

            // 卡片ID选择
            Column() {
              Text('选择卡片ID')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 20 })
                .fontColor($r('sys.color.font_primary'))

              // 下拉菜单容器
              Column() {
                // 下拉菜单触发器
                Row() {
                  if (this.scriptManager.cardIdList.length === 0) {
                    Text('加载中...')
                      .fontSize(16)
                      .fontColor($r('sys.color.font_secondary'))
                      .layoutWeight(1)
                  } else {
                    Text(this.selectedCardId)
                      .fontSize(16)
                      .fontColor(this.selectedCardId ? $r('sys.color.font_primary') : $r('sys.color.font_secondary'))
                      .layoutWeight(1)
                  }

                  if (this.scriptManager.cardIdList.length === 0) {
                    LoadingProgress()
                      .width(16)
                      .height(16)
                      .color($r('sys.color.font_secondary'))
                  } else {
                    Text(this.isCardIdDropdownOpen ? '▲' : '▼')
                      .fontSize(14)
                      .fontColor($r('sys.color.font_secondary'))
                      .animation({
                        duration: 200,
                        curve: Curve.EaseInOut
                      })
                  }
                }
                .width('100%')
                .height(48)
                .padding({ left: 16, right: 16 })
                .backgroundColor($r('sys.color.background_secondary'))
                .borderRadius(12)
                .justifyContent(FlexAlign.SpaceBetween)
                .alignItems(VerticalAlign.Center)
                .onClick(() => {
                  if (this.scriptManager.cardIdList.length > 0) {
                    this.isCardIdDropdownOpen = !this.isCardIdDropdownOpen;
                  }
                })

                // 下拉选项列表
                if (this.isCardIdDropdownOpen && this.scriptManager.cardIdList.length > 0) {
                  Column() {
                    if (this.scriptManager.cardIdList.length === 0) {
                      // 无可用卡片ID时的提示
                      Row() {
                        Text('暂无可用的卡片ID')
                          .fontSize(16)
                          .fontColor($r('sys.color.font_secondary'))
                      }
                      .width('100%')
                      .height(44)
                      .padding({ left: 16, right: 16 })
                      .justifyContent(FlexAlign.Center)
                      .alignItems(VerticalAlign.Center)
                    } else {
                      ForEach(this.scriptManager.cardIdList, (cardId: string, index: number) => {
                        Row() {
                          Text(cardId)
                            .fontSize(16)
                            .fontColor(this.selectedCardId === cardId ? '#007AFF' : $r('sys.color.font_primary'))
                            .fontWeight(this.selectedCardId === cardId ? FontWeight.Medium : FontWeight.Normal)
                        }
                        .width('100%')
                        .height(44)
                        .padding({ left: 16, right: 16 })
                        .backgroundColor(this.selectedCardId === cardId ? '#E8F5FE' : Color.Transparent)
                        .justifyContent(FlexAlign.Start)
                        .alignItems(VerticalAlign.Center)
                        .onClick(() => {
                          this.selectedCardId = cardId;
                          this.isCardIdDropdownOpen = false;
                        })
                        .hoverEffect(HoverEffect.Scale)

                        if (index < this.scriptManager.cardIdList.length - 1) {
                          Divider()
                            .color('#F0F0F0')
                            .strokeWidth(1)
                        }
                      }, (cardId: string) => cardId)
                    }
                  }
                  .width('100%')
                  .backgroundColor(Color.White)
                  .borderRadius(12)
                  .margin({ top: 4 })
                  .shadow({
                    radius: 8,
                    color: 'rgba(0, 0, 0, 0.15)',
                    offsetX: 0,
                    offsetY: 4
                  })
                  .animation({
                    duration: 200,
                    curve: Curve.EaseInOut
                  })
                }
              }
              .width('100%')
            }
            .width('100%')
            .padding(20)
            .backgroundColor(Color.White)
            .borderRadius(16)
            .margin({ top: 16, left: 16, right: 16 })
            .shadow({
              radius: 8,
              color: 'rgba(0, 0, 0, 0.08)',
              offsetX: 0,
              offsetY: 2
            })
          }
          .width('100%')
          .padding({ top: 0, bottom: 100 })  // 将顶部间距从16改为0，使内容更靠近标题栏
        }
        .layoutWeight(1)
        .scrollBar(BarState.Auto)
        .edgeEffect(EdgeEffect.Spring)

        // 底部添加按钮
        Button(this.scriptManager.cardIdList.length === 0 ? '加载中...' : '立即添加')
          .width('90%')
          .height(48)
          .margin({ bottom: 20 })  // 距离底部20px
          .enabled(this.scriptManager.cardIdList.length > 0 && this.cardName.length > 0 && this.selectedCardId.length > 0)
          .backgroundColor((this.scriptManager.cardIdList.length > 0 && this.cardName.length > 0 && this.selectedCardId.length > 0) ? '#007AFF' : '#CCCCCC')
          .borderRadius(24)
          .fontColor(Color.White)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .onClick(async () => {
            // 检查卡片ID是否已存在
            const existingCard = this.cardManager.cards.find(card => card.id === this.selectedCardId);
            if (existingCard) {
              promptAction.showToast({
                message: '该卡片ID已存在，请选择其他ID',
                duration: 2000
              });
              return;
            }

            // 创建卡片
            const newCard = new CardModel(
              this.selectedCardId,
              this.cardName,
              this.cardType
            );

            // 添加卡片
            const success = await this.cardManager.addCard(newCard);

            if (success) {
              // 提示添加成功
              promptAction.showToast({
                message: '添加卡片成功',
                duration: 2000
              });

              // 添加成功后返回主页
              NavigationUtils.pop();
            }
          })
      }
      .width('100%')
      .height('100%')
      .backgroundColor('#F8F8F8')
    }
    .title('添加智能卡')
    .onBackPressed(() => {
      NavigationUtils.pop();
      return true;
    })
  }
}
