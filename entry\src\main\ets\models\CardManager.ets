import { hilog } from '@kit.PerformanceAnalysisKit';
import { DialogHelper } from '../components/CommonDialog';
import { preferences } from '@kit.ArkData';
import { Context } from '@kit.AbilityKit';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-CardManager';
const PREFERENCES_NAME = 'TmsWalletPrefs';
const CARDS_KEY = 'cards_data';

/**
 * 卡片序列化数据接口
 */
interface CardData {
  id: string;
  name: string;
  cardType: string;
  createTime: number;
  isSelected: boolean;
}

/**
 * 智能卡模型
 */
@Observed
export class CardModel {
  id: string;             // 脚本名
  name: string;           // 用户输入的卡片名称
  cardType: string;       // 卡片类型（复制卡/添加卡）
  createTime: number;     // 添加卡片的时间
  isSelected: boolean;    // 是否被选中

  constructor(id: string, name: string, cardType: string, createTime?: number, isSelected: boolean = false) {
    this.id = id;
    this.name = name;
    this.cardType = cardType;
    this.createTime = createTime || Date.now();
    this.isSelected = isSelected;
  }
}

/**
 * 卡片管理器
 */
@Observed
export class CardManager {
  private static instance: CardManager;
  @Track cards: CardModel[] = [];
  @Track isMenuOpen: boolean = false;
  private preferencesStore: preferences.Preferences | null = null;

  public static getInstance(): CardManager {
    if (!CardManager.instance) {
      CardManager.instance = new CardManager();
    }
    return CardManager.instance;
  }

  /**
   * 从存储中加载卡片数据
   */
  public async loadCardsFromStorage(context: Context): Promise<void> {
    try {
      // 初始化preferences存储
      this.preferencesStore = await preferences.getPreferences(context, PREFERENCES_NAME);

      const cardsDataStr = await this.preferencesStore.get(CARDS_KEY, '[]') as string;
      const cardsData: CardData[] = JSON.parse(cardsDataStr) as CardData[];

      // 将数据转换为CardModel对象
      this.cards = cardsData.map((data: CardData) => new CardModel(
        data.id,
        data.name,
        data.cardType,
        data.createTime,
        data.isSelected
      ));

      hilog.info(DOMAIN, TAG, '从存储加载卡片数据成功，数量: %{public}d', this.cards.length);
    } catch (error) {
      hilog.error(DOMAIN, TAG, '从存储加载卡片数据失败: %{public}s', error);
      this.cards = [];
    }
  }

  /**
   * 保存卡片数据到存储
   */
  public async saveCardsToStorage(): Promise<boolean> {
    try {
      if (!this.preferencesStore) {
        hilog.error(DOMAIN, TAG, 'preferences存储未初始化');
        return false;
      }

      // 将卡片数据转换为可序列化的格式
      const cardsData: CardData[] = this.cards.map((card: CardModel): CardData => {
        return {
          id: card.id,
          name: card.name,
          cardType: card.cardType,
          createTime: card.createTime,
          isSelected: card.isSelected
        };
      });

      // 保存到preferences
      await this.preferencesStore.put(CARDS_KEY, JSON.stringify(cardsData));
      await this.preferencesStore.flush();

      hilog.info(DOMAIN, TAG, '保存卡片数据到存储成功，数量: %{public}d', this.cards.length);
      return true;
    } catch (error) {
      hilog.error(DOMAIN, TAG, '保存卡片数据到存储失败: %{public}s', error);
      return false;
    }
  }



  /**
   * 添加卡片
   */
  public async addCard(card: CardModel): Promise<boolean> {
    try {
      hilog.info(DOMAIN, TAG, '开始添加卡片: %{public}s', JSON.stringify(card));

      // 如果是第一张卡片，默认选中；否则不选中
      card.isSelected = this.cards.length === 0;

      // 创建新数组来触发状态更新
      this.cards = [...this.cards, card];

      // 添加成功后保存到存储
      const saveSuccess = await this.saveCardsToStorage();
      if (!saveSuccess) {
        hilog.warn(DOMAIN, TAG, '添加卡片成功但保存到存储失败');
      }

      hilog.info(DOMAIN, TAG, '添加卡片 %{public}s 成功，当前卡片数量: %{public}d', card.name, this.cards.length);
      return true;
    } catch (error) {
      hilog.error(DOMAIN, TAG, '添加卡片失败: %{public}s', error);
      return false;
    }
  }

  /**
   * 删除选中的卡片
   */
  public async deleteCard(): Promise<boolean> {
    try {
      const selectedCard = this.cards.find(card => card.isSelected);
      if (!selectedCard) {
        DialogHelper.showToast('请先选择要删除的卡片');
        return false;
      }

      const confirmed = await DialogHelper.showConfirmDialog({
        title: '确认删除',
        message: `确定要删除 "${selectedCard.name}" 吗？`
      });

      if (!confirmed) {
        return false;
      }

      // 删除选中的卡片，创建新数组来触发状态更新
      this.cards = this.cards.filter(card => card.id !== selectedCard.id);

      // 删除成功后保存到存储
      const saveSuccess = await this.saveCardsToStorage();
      if (!saveSuccess) {
        hilog.warn(DOMAIN, TAG, '删除卡片成功但保存到存储失败');
      }

      this.isMenuOpen = false;
      DialogHelper.showToast('删除成功');
      hilog.info(DOMAIN, TAG, '删除卡片 %{public}s 成功，剩余卡片数量: %{public}d', selectedCard.name, this.cards.length);
      return true;
    } catch (error) {
      hilog.error(DOMAIN, TAG, '删除卡片失败: %{public}s', error);
      DialogHelper.showToast('删除失败');
      return false;
    }
  }

  /**
   * 选择卡片
   */
  public selectCard(cardId: string): void {
    hilog.info(DOMAIN, TAG, '尝试选择卡片: %{public}s', cardId);

    // 检查卡片是否存在
    const targetCard = this.cards.find(card => card.id === cardId);
    if (!targetCard) {
      hilog.warn(DOMAIN, TAG, '卡片不存在: %{public}s', cardId);
      return;
    }

    // 直接修改现有对象的属性，不重新创建对象
    this.cards.forEach(card => {
      card.isSelected = (card.id === cardId);
    });

    hilog.info(DOMAIN, TAG, '卡片选择成功: %{public}s', cardId);
  }
}