/**
 * 页面信息接口
 */
interface PageInfo {
  level: number;
  title: string;
  children?: string[];
  parent?: string;
}

/**
 * Navigation路由管理器
 */
export class NavigationManager {
  // 路由名称常量
  static readonly MAIN_PAGE: string = 'MainPage';
  static readonly ADD_CARD_PAGE: string = 'AddCardPage';
  static readonly HCE_MANAGER_PAGE: string = 'HceManagerPage';

  // 页面层级关系
  private static readonly PAGE_HIERARCHY: Record<string, PageInfo> = {
    'MainPage': {
      level: 0,
      title: '卡包',
      children: ['AddCardPage', 'HceManagerPage']
    },
    'AddCardPage': {
      level: 1,
      title: '添加智能卡',
      parent: 'MainPage'
    },
    'HceManagerPage': {
      level: 1,
      title: 'HCE管理',
      parent: 'MainPage'
    }
  };

  /**
   * 获取页面信息
   * @param routeName 路由名称
   * @returns 页面信息
   */
  static getPageInfo(routeName: string): PageInfo | undefined {
    return NavigationManager.PAGE_HIERARCHY[routeName];
  }

  /**
   * 判断是否为主页
   * @param routeName 路由名称
   * @returns 是否为主页
   */
  static isMainPage(routeName: string): boolean {
    return routeName === NavigationManager.MAIN_PAGE;
  }

  /**
   * 判断是否为子页
   * @param routeName 路由名称
   * @returns 是否为子页
   */
  static isSubPage(routeName: string): boolean {
    const pageInfo = NavigationManager.getPageInfo(routeName);
    return pageInfo !== undefined && pageInfo.level > 0;
  }
}

/**
 * Navigation工具类
 */
export class NavigationUtils {
  private static navigationStack: NavPathStack | null = null;

  /**
   * 设置Navigation栈
   * @param stack Navigation栈实例
   */
  static setNavigationStack(stack: NavPathStack): void {
    NavigationUtils.navigationStack = stack;
  }

  /**
   * 获取Navigation栈
   * @returns Navigation栈实例
   */
  static getNavigationStack(): NavPathStack | null {
    return NavigationUtils.navigationStack;
  }

  /**
   * 跳转到子页面
   * @param routeName 路由名称
   * @param param 传递的参数
   */
  static pushSubPage(routeName: string, param?: Object): void {
    if (NavigationUtils.navigationStack && NavigationManager.isSubPage(routeName)) {
      NavigationUtils.navigationStack.pushPath({ name: routeName, param: param });
    }
  }

  /**
   * 跳转到指定页面（通用方法）
   * @param routeName 路由名称
   * @param param 传递的参数
   */
  static pushPath(routeName: string, param?: Object): void {
    if (NavigationUtils.navigationStack) {
      NavigationUtils.navigationStack.pushPath({ name: routeName, param: param });
    }
  }

  /**
   * 返回上一页（从子页返回主页）
   */
  static pop(): void {
    if (NavigationUtils.navigationStack) {
      NavigationUtils.navigationStack.pop();
    }
  }

  /**
   * 返回到主页
   */
  static popToMainPage(): void {
    if (NavigationUtils.navigationStack) {
      NavigationUtils.navigationStack.popToName(NavigationManager.MAIN_PAGE);
    }
  }

  /**
   * 返回到指定页面
   * @param routeName 路由名称
   */
  static popToName(routeName: string): void {
    if (NavigationUtils.navigationStack) {
      NavigationUtils.navigationStack.popToName(routeName);
    }
  }

  /**
   * 清空栈
   */
  static clear(): void {
    if (NavigationUtils.navigationStack) {
      NavigationUtils.navigationStack.clear();
    }
  }

  /**
   * 替换当前页面
   * @param routeName 路由名称
   * @param param 传递的参数
   */
  static replacePath(routeName: string, param?: Object): void {
    if (NavigationUtils.navigationStack) {
      NavigationUtils.navigationStack.replacePath({ name: routeName, param: param });
    }
  }

  /**
   * 获取当前页面栈的大小
   * @returns 栈大小
   */
  static getStackSize(): number {
    if (NavigationUtils.navigationStack) {
      return NavigationUtils.navigationStack.size();
    }
    return 0;
  }

  /**
   * 获取当前页面信息
   * @returns 当前页面信息
   */
  static getCurrentPageInfo(): PageInfo | undefined {
    if (NavigationUtils.navigationStack && NavigationUtils.navigationStack.size() > 0) {
      const pathInfo = NavigationUtils.navigationStack.getAllPathName();
      const currentPageName = pathInfo[pathInfo.length - 1];
      return NavigationManager.getPageInfo(currentPageName);
    }
    return NavigationManager.getPageInfo(NavigationManager.MAIN_PAGE);
  }
}
